# =============================================================================
# Step 1: Setup and Import Libraries 
# =============================================================================
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings

warnings.filterwarnings('ignore')

# Data Preprocessing
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
from sklearn.impute import SimpleImputer

# Imbalance Handling
from imblearn.over_sampling import BorderlineSMOTE
from imblearn.under_sampling import NearMiss
from imblearn.pipeline import Pipeline as ImbPipeline

# Base Models
from sklearn.ensemble import RandomForestClassifier, AdaBoostClassifier
from sklearn.svm import SVC
from sklearn.tree import DecisionTreeClassifier

# Meta Models (Deep Learning)
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, GRU, Bidirectional, Conv1D, MaxPooling1D, Flatten, Dense, Dropout
from tensorflow.keras.wrappers.scikit_learn import KerasClassifier

# Stacking and Evaluation
from sklearn.ensemble import StackingClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

# Set a global random state for full reproducibility
RANDOM_STATE = 42

# Set plotting style
sns.set_style("whitegrid")
print("TensorFlow Version:", tf.__version__)
print("Libraries imported successfully.")

# =============================================================================
# Step 2: Data Loading and Configuration
# =============================================================================
file_path = "data/dataset.csv"
target_column_name = 'FLAG'

# Load the dataset
try:
    df = pd.read_csv(file_path)
    print(f"Successfully loaded dataset from '{file_path}'. Shape: {df.shape}")
except FileNotFoundError:
    print(f"ERROR: The file '{file_path}' was not found. Please check the path.")
    assert False, "File not found."

# --- Data Verification ---
if target_column_name not in df.columns:
    print(f"ERROR: The target column '{target_column_name}' was not found in the dataset.")
    print(f"Available columns are: {df.columns.tolist()}")
    assert False, "Target column not found."

print(f"\nValue counts for the target column ('{target_column_name}'):")
print(df[target_column_name].value_counts())

# =============================================================================
# Step 3: Preprocessing and Resampling (This cell creates X_train_res)
# =============================================================================


# 3.1. Define Features (X) and Target (y)
X = df.drop(target_column_name, axis=1)
y = df[target_column_name]

# Identify and handle non-numeric columns
numeric_cols = X.select_dtypes(include=np.number).columns.tolist()
if len(numeric_cols) != X.shape[1]:
    non_numeric_cols = list(set(X.columns) - set(numeric_cols))
    print(f"Warning: Dropping non-numeric feature columns: {non_numeric_cols}")
    X = X[numeric_cols]

# 3.2. Data Interpolation for Missing Values
print("\nInterpolating missing values...")
X = X.interpolate(method='linear', limit_direction='both', axis=1).fillna(0) # fillna(0) for any remaining NaNs

# 3.3. Split data: 80% train, 20% test
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=RANDOM_STATE, stratify=y)
print(f"Initial training set shape: {X_train.shape}")
print(f"Testing set shape: {X_test.shape}")

# 3.4. Normalization using MinMaxScaler
scaler = MinMaxScaler()
X_train_scaled_orig = scaler.fit_transform(X_train) # Scale before resampling
X_test_scaled = scaler.transform(X_test)
print("Data normalized.")

# 3.5. Hybrid Resampling Pipeline (Borderline-SMOTE + NearMiss-2)
resampling_pipeline = ImbPipeline(steps=[
    ('bsmote', BorderlineSMOTE(random_state=RANDOM_STATE, n_jobs=-1)),
    ('nearmiss', NearMiss(version=2, n_jobs=-1))
])

print("\nBalancing training data using Hybrid Resampling (BSMOTE + NM-2)...")
# Important: We resample the scaled training data
X_train_res, y_train_res = resampling_pipeline.fit_resample(X_train_scaled_orig, y_train)
print("Balancing complete.")
print(f"Class distribution in training data AFTER balancing:\n{y_train_res.value_counts()}")

# =============================================================================
# Step 4: Model Definition and Training (This cell uses X_train_res)
# =============================================================================

# --- 4.1. Base Classifiers  ---
rf_base = RandomForestClassifier(n_estimators=100, max_depth=None, min_samples_split=2, random_state=RANDOM_STATE, n_jobs=-1)
svm_base = SVC(kernel='rbf', C=1.0, gamma='scale', probability=True, random_state=RANDOM_STATE)
adaboost_base = AdaBoostClassifier(n_estimators=50, learning_rate=1.0, random_state=RANDOM_STATE)
base_models_list = [('rf', rf_base), ('svm', svm_base), ('adaboost', adaboost_base)]

# --- 4.2. Meta Classifiers (Deep Learning Wrappers) ---
n_base_models = len(base_models_list)
input_shape_dl = (n_base_models, 1)

def create_lstm_meta_model(input_shape=input_shape_dl):
    model = Sequential([LSTM(50, input_shape=input_shape), Dropout(0.2), Dense(1, activation='sigmoid')])
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
    return model

def create_bilstm_meta_model(input_shape=input_shape_dl):
    model = Sequential([Bidirectional(LSTM(50), input_shape=input_shape), Dropout(0.2), Dense(1, activation='sigmoid')])
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
    return model

def create_gru_meta_model(input_shape=input_shape_dl):
    model = Sequential([GRU(50, input_shape=input_shape), Dropout(0.2), Dense(1, activation='sigmoid')])
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
    return model
    
def create_bigru_meta_model(input_shape=input_shape_dl):
    model = Sequential([Bidirectional(GRU(50), input_shape=input_shape), Dropout(0.2), Dense(1, activation='sigmoid')])
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
    return model

def create_cnn_meta_model(input_shape=input_shape_dl):
    model = Sequential([Conv1D(filters=32, kernel_size=2, activation='relu', input_shape=input_shape), MaxPooling1D(pool_size=1), Flatten(), Dense(1, activation='sigmoid')])
    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
    return model

meta_classifiers = {
    "LSTM": KerasClassifier(build_fn=create_lstm_meta_model, epochs=10, batch_size=64, verbose=0),
    "Bi-LSTM": KerasClassifier(build_fn=create_bilstm_meta_model, epochs=10, batch_size=64, verbose=0),
    "GRU": KerasClassifier(build_fn=create_gru_meta_model, epochs=10, batch_size=64, verbose=0),
    "Bi-GRU": KerasClassifier(build_fn=create_bigru_meta_model, epochs=10, batch_size=64, verbose=0),
    "CNN": KerasClassifier(build_fn=create_cnn_meta_model, epochs=10, batch_size=64, verbose=0)
}

# --- 4.3. Train All Models ---
trained_models = {}
print("--- Training Base Models for Standalone Evaluation ---")
for name, model in base_models_list:
    print(f"Training {name.upper()}...")
    model.fit(X_train_res, y_train_res)
    trained_models[name.upper()] = model

print("\n--- Training Stacked Models with DL Meta-Classifiers ---")
for meta_name, meta_model in meta_classifiers.items():
    classifier_name = f'SEM-{meta_name}'
    print(f"Training {classifier_name}...")
    stacker = StackingClassifier(estimators=base_models_list, final_estimator=meta_model, cv=3, n_jobs=-1)
    stacker.fit(X_train_res, y_train_res)
    trained_models[classifier_name] = stacker

print("\nAll models have been trained successfully.")

# =============================================================================
# Step 5: Final Evaluation 
# =============================================================================


results = []
print("\n--- Evaluating All Trained Models ---")
for name, model in trained_models.items():
    print(f"Evaluating {name}...")
    y_pred = model.predict(X_test_scaled)
    if y_pred.ndim > 1 and y_pred.shape[1] > 0:
        y_pred = (y_pred > 0.5).astype("int32").flatten()

    results.append({
        'Classifier': name,
        'Accuracy': accuracy_score(y_test, y_pred),
        'Precision': precision_score(y_test, y_pred, zero_division=0),
        'Recall': recall_score(y_test, y_pred, zero_division=0),
        'F1-score': f1_score(y_test, y_pred, zero_division=0)
    })

results_df = pd.DataFrame(results).set_index('Classifier')
print("\n--- Final Performance Results (Hybrid Resampling) ---")
display(results_df.style.format("{:.4f}"))