{"cells": [{"cell_type": "code", "execution_count": null, "id": "0332d89a-3e05-4228-bd6c-18d9f0334d86", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TensorFlow Version: 2.10.0\n", "Libraries imported successfully.\n"]}], "source": ["# =============================================================================\n", "# Step 1: Setup and Import Libraries \n", "# =============================================================================\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# Data Preprocessing\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.impute import SimpleImputer\n", "\n", "# Imbalance Handling\n", "from imblearn.over_sampling import BorderlineSMOTE\n", "from imblearn.under_sampling import NearMiss\n", "from imblearn.pipeline import Pipeline as ImbPipeline\n", "\n", "# Base Models\n", "from sklearn.ensemble import RandomForestClassifier, AdaBoostClassifier\n", "from sklearn.svm import SVC\n", "from sklearn.tree import DecisionTreeClassifier\n", "\n", "# Meta Models (Deep Learning)\n", "import tensorflow as tf\n", "from tensorflow.keras.models import Sequential\n", "from tensorflow.keras.layers import LSTM, GRU, Bidirectional, Conv1D, MaxPooling1D, Flatten, Dense, Dropout\n", "from tensorflow.keras.wrappers.scikit_learn import KerasClassifier\n", "\n", "# Stacking and Evaluation\n", "from sklearn.ensemble import StackingClassifier\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score\n", "\n", "# Set a global random state for full reproducibility\n", "RANDOM_STATE = 42\n", "\n", "# Set plotting style\n", "sns.set_style(\"whitegrid\")\n", "print(\"TensorFlow Version:\", tf.__version__)\n", "print(\"Libraries imported successfully.\")"]}, {"cell_type": "code", "execution_count": null, "id": "f3fc9348-a591-45ec-a1cc-a50c999082ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully loaded dataset from 'data/dataset.csv'. Shape: (42372, 1036)\n", "\n", "Value counts for the target column ('FLAG'):\n", "FLAG\n", "0    38757\n", "1     3615\n", "Name: count, dtype: int64\n"]}], "source": ["# =============================================================================\n", "# Step 2: Data Loading and Configuration\n", "# =============================================================================\n", "file_path = \"data/dataset.csv\"\n", "target_column_name = 'FLAG'\n", "\n", "# Load the dataset\n", "try:\n", "    df = pd.read_csv(file_path)\n", "    print(f\"Successfully loaded dataset from '{file_path}'. Shape: {df.shape}\")\n", "except FileNotFoundError:\n", "    print(f\"ERROR: The file '{file_path}' was not found. Please check the path.\")\n", "    assert False, \"File not found.\"\n", "\n", "# --- Data Verification ---\n", "if target_column_name not in df.columns:\n", "    print(f\"ERROR: The target column '{target_column_name}' was not found in the dataset.\")\n", "    print(f\"Available columns are: {df.columns.tolist()}\")\n", "    assert False, \"Target column not found.\"\n", "\n", "print(f\"\\nValue counts for the target column ('{target_column_name}'):\")\n", "print(df[target_column_name].value_counts())"]}, {"cell_type": "code", "execution_count": null, "id": "d2ec62c4-d18a-4e4f-9a43-9cf1cab1e2fc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Warning: Dropping non-numeric feature columns: ['CONS_NO']\n", "\n", "Interpolating missing values...\n", "Initial training set shape: (33897, 1034)\n", "Testing set shape: (8475, 1034)\n", "Data normalized.\n", "\n", "Balancing training data using Hybrid Resampling (BSMOTE + NM-2)...\n", "Balancing complete.\n", "Class distribution in training data AFTER balancing:\n", "FLAG\n", "0    31005\n", "1    31005\n", "Name: count, dtype: int64\n"]}], "source": ["# =============================================================================\n", "# Step 3: Preprocessing and Resampling (This cell creates X_train_res)\n", "# =============================================================================\n", "\n", "\n", "# 3.1. Define Features (X) and Target (y)\n", "X = df.drop(target_column_name, axis=1)\n", "y = df[target_column_name]\n", "\n", "# Identify and handle non-numeric columns\n", "numeric_cols = X.select_dtypes(include=np.number).columns.tolist()\n", "if len(numeric_cols) != X.shape[1]:\n", "    non_numeric_cols = list(set(X.columns) - set(numeric_cols))\n", "    print(f\"Warning: Dropping non-numeric feature columns: {non_numeric_cols}\")\n", "    X = X[numeric_cols]\n", "\n", "# 3.2. Data Interpolation for Missing Values\n", "print(\"\\nInterpolating missing values...\")\n", "X = X.interpolate(method='linear', limit_direction='both', axis=1).fillna(0) # fillna(0) for any remaining NaNs\n", "\n", "# 3.3. Split data: 80% train, 20% test\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=RANDOM_STATE, stratify=y)\n", "print(f\"Initial training set shape: {X_train.shape}\")\n", "print(f\"Testing set shape: {X_test.shape}\")\n", "\n", "# 3.4. Normalization using MinMaxScaler\n", "scaler = MinMaxScaler()\n", "X_train_scaled_orig = scaler.fit_transform(X_train) # Scale before resampling\n", "X_test_scaled = scaler.transform(X_test)\n", "print(\"Data normalized.\")\n", "\n", "# 3.5. Hybrid Resampling Pipeline (Borderline-SMOTE + NearMiss-2)\n", "resampling_pipeline = ImbPipeline(steps=[\n", "    ('bsmote', BorderlineSMOTE(random_state=RANDOM_STATE, n_jobs=-1)),\n", "    ('nearmiss', NearMiss(version=2, n_jobs=-1))\n", "])\n", "\n", "print(\"\\nBalancing training data using Hybrid Resampling (BSMOTE + NM-2)...\")\n", "# Important: We resample the scaled training data\n", "X_train_res, y_train_res = resampling_pipeline.fit_resample(X_train_scaled_orig, y_train)\n", "print(\"Balancing complete.\")\n", "print(f\"Class distribution in training data AFTER balancing:\\n{y_train_res.value_counts()}\")"]}, {"cell_type": "code", "execution_count": null, "id": "17fbcf2c-a98a-427a-a77a-c5fdeac696de", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Training Base Models for Standalone Evaluation ---\n", "Training RF...\n", "Training SVM...\n"]}], "source": ["# =============================================================================\n", "# Step 4: Model Definition and Training (This cell uses X_train_res)\n", "# =============================================================================\n", "\n", "# --- 4.1. Base Classifiers  ---\n", "rf_base = RandomForestClassifier(n_estimators=100, max_depth=None, min_samples_split=2, random_state=RANDOM_STATE, n_jobs=-1)\n", "svm_base = SVC(kernel='rbf', C=1.0, gamma='scale', probability=True, random_state=RANDOM_STATE)\n", "adaboost_base = AdaBoostClassifier(n_estimators=50, learning_rate=1.0, random_state=RANDOM_STATE)\n", "base_models_list = [('rf', rf_base), ('svm', svm_base), ('adaboost', adaboost_base)]\n", "\n", "# --- 4.2. <PERSON><PERSON> Classifiers (Deep Learning Wrappers) ---\n", "n_base_models = len(base_models_list)\n", "input_shape_dl = (n_base_models, 1)\n", "\n", "def create_lstm_meta_model(input_shape=input_shape_dl):\n", "    model = Sequential([LSTM(50, input_shape=input_shape), Dropout(0.2), Dense(1, activation='sigmoid')])\n", "    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])\n", "    return model\n", "\n", "def create_bilstm_meta_model(input_shape=input_shape_dl):\n", "    model = Sequential([Bidirectional(LSTM(50), input_shape=input_shape), Dropout(0.2), Dense(1, activation='sigmoid')])\n", "    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])\n", "    return model\n", "\n", "def create_gru_meta_model(input_shape=input_shape_dl):\n", "    model = Sequential([GRU(50, input_shape=input_shape), Dropout(0.2), Dense(1, activation='sigmoid')])\n", "    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])\n", "    return model\n", "    \n", "def create_bigru_meta_model(input_shape=input_shape_dl):\n", "    model = Sequential([Bidirectional(GRU(50), input_shape=input_shape), Dropout(0.2), Dense(1, activation='sigmoid')])\n", "    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])\n", "    return model\n", "\n", "def create_cnn_meta_model(input_shape=input_shape_dl):\n", "    model = Sequential([Conv1D(filters=32, kernel_size=2, activation='relu', input_shape=input_shape), MaxPooling1D(pool_size=1), Flatten(), Dense(1, activation='sigmoid')])\n", "    model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])\n", "    return model\n", "\n", "meta_classifiers = {\n", "    \"LSTM\": KerasClassifier(build_fn=create_lstm_meta_model, epochs=10, batch_size=64, verbose=0),\n", "    \"Bi-LSTM\": KerasClassifier(build_fn=create_bilstm_meta_model, epochs=10, batch_size=64, verbose=0),\n", "    \"GRU\": KerasClassifier(build_fn=create_gru_meta_model, epochs=10, batch_size=64, verbose=0),\n", "    \"Bi-GRU\": KerasClassifier(build_fn=create_bigru_meta_model, epochs=10, batch_size=64, verbose=0),\n", "    \"CNN\": KerasClassifier(build_fn=create_cnn_meta_model, epochs=10, batch_size=64, verbose=0)\n", "}\n", "\n", "# --- 4.3. Train All Models ---\n", "trained_models = {}\n", "print(\"--- Training Base Models for Standalone Evaluation ---\")\n", "for name, model in base_models_list:\n", "    print(f\"Training {name.upper()}...\")\n", "    model.fit(X_train_res, y_train_res)\n", "    trained_models[name.upper()] = model\n", "\n", "print(\"\\n--- Training Stacked Models with DL Meta-Classifiers ---\")\n", "for meta_name, meta_model in meta_classifiers.items():\n", "    classifier_name = f'SEM-{meta_name}'\n", "    print(f\"Training {classifier_name}...\")\n", "    stacker = StackingClassifier(estimators=base_models_list, final_estimator=meta_model, cv=3, n_jobs=-1)\n", "    stacker.fit(X_train_res, y_train_res)\n", "    trained_models[classifier_name] = stacker\n", "\n", "print(\"\\nAll models have been trained successfully.\")"]}, {"cell_type": "code", "execution_count": null, "id": "7cc6bd3a-0108-455e-801b-c04ae9561432", "metadata": {}, "outputs": [], "source": ["# =============================================================================\n", "# Step 5: Final Evaluation \n", "# =============================================================================\n", "\n", "\n", "results = []\n", "print(\"\\n--- Evaluating All Trained Models ---\")\n", "for name, model in trained_models.items():\n", "    print(f\"Evaluating {name}...\")\n", "    y_pred = model.predict(X_test_scaled)\n", "    if y_pred.ndim > 1 and y_pred.shape[1] > 0:\n", "        y_pred = (y_pred > 0.5).astype(\"int32\").flatten()\n", "\n", "    results.append({\n", "        'Classifier': name,\n", "        'Accuracy': accuracy_score(y_test, y_pred),\n", "        'Precision': precision_score(y_test, y_pred, zero_division=0),\n", "        'Recall': recall_score(y_test, y_pred, zero_division=0),\n", "        'F1-score': f1_score(y_test, y_pred, zero_division=0)\n", "    })\n", "\n", "results_df = pd.DataFrame(results).set_index('Classifier')\n", "print(\"\\n--- Final Performance Results (Hybrid Resampling) ---\")\n", "display(results_df.style.format(\"{:.4f}\"))"]}], "metadata": {"kernelspec": {"display_name": "Python (py39)", "language": "python", "name": "py39"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}